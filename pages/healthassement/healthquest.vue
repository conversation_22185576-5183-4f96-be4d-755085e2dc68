<template>
	<view class="container">
		<!-- 顶部说明 -->
		<view class="instruction-header">
			<text class="instruction-text">以下问题请根据您近一年的体验和感觉回答，孕妇及18岁以下人群不建议使用本测试。</text>
		</view>

		<!-- 问题容器 -->
		<scroll-view class="question-container" scroll-y="true">
			<view 
				v-for="(question, index) in questions" 
				:key="question.id"
				:class="['question-card', { 'transparent': question.transparent }]"
			>
				<text class="question-text">{{ question.text }}</text>
				<view class="pill-button-group">
					<view 
						v-for="option in question.options" 
						:key="option.value"
						:class="['pill-button', { 'selected': question.selectedValue === option.value }]"
						@tap="selectOption(question.id, option.value)"
					>
						<text class="pill-text">{{ option.label }}</text>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 提交按钮 -->
		<view class="submit-button" @tap="submitQuestionnaire">
			<text class="submit-text">查看结果</text>
		</view>
	</view>
</template>

<script>
	const util = require("@/utils/util.js");
	const api = require('@/utils/api.js');
	
	export default {
		data() {
			return {
				questions: [],
				loading: false
			}
		},
		methods: {
			// 获取问卷数据
			getQuestionnaireData() {
				uni.showLoading({
					title: '加载中...'
				});
				
				// 使用模拟数据，实际项目中可以取消注释下面的API调用
				this.questions = this.getMockQuestions();
				
				setTimeout(() => {
					uni.hideLoading();
				}, 1000);
				
				// 真实API调用（注释状态）
				// let that = this;
				// util.request(api.HealthQuestionnaire).then(function(res) {
				// 	if (res.errno === 0) {
				// 		that.questions = res.data.questions;
				// 	}
				// });
			},
			
			// 获取模拟问题数据
			getMockQuestions() {
				return [
					{
						id: 'question_1',
						text: '1. 您是否每天精力都很充沛？',
						transparent: false,
						selectedValue: 'no',
						options: [
							{ label: '是', value: 'yes' },
							{ label: '否', value: 'no' }
						]
					},
					{
						id: 'question_2',
						text: '2. 您是否心态平和？',
						transparent: true,
						selectedValue: 'no',
						options: [
							{ label: '是', value: 'yes' },
							{ label: '否', value: 'no' }
						]
					},
					{
						id: 'question_3',
						text: '3. 您是否面色很红润？',
						transparent: true,
						selectedValue: 'no',
						options: [
							{ label: '是', value: 'yes' },
							{ label: '否', value: 'no' }
						]
					},
					{
						id: 'question_4',
						text: '4. 您是否经常感到闷闷不乐？',
						transparent: false,
						selectedValue: 'yes',
						options: [
							{ label: '是', value: 'yes' },
							{ label: '否', value: 'no' }
						]
					},
					{
						id: 'question_5',
						text: '5. 您是否经常忘事，丢三落四的？',
						transparent: true,
						selectedValue: 'yes',
						options: [
							{ label: '是', value: 'yes' },
							{ label: '否', value: 'no' }
						]
					},
					{
						id: 'question_6',
						text: '6. 您是否经常容易心慌慌？',
						transparent: false,
						selectedValue: 'yes',
						options: [
							{ label: '是', value: 'yes' },
							{ label: '否', value: 'no' }
						]
					},
					{
						id: 'question_7',
						text: '7. 是否需要进行体质调理师一对一私人咨询？',
						transparent: true,
						selectedValue: 'yes',
						options: [
							{ label: '需要', value: 'yes' },
							{ label: '不需要', value: 'no' }
						]
					}
				];
			},
			
			// 选择选项
			selectOption(questionId, value) {
				const question = this.questions.find(q => q.id === questionId);
				if (question) {
					question.selectedValue = value;
				}
			},
			
			// 提交问卷
			submitQuestionnaire() {
				// 收集答案
				const answers = {};
				this.questions.forEach(question => {
					answers[question.id] = question.selectedValue;
				});
				
				uni.showLoading({
					title: '提交中...'
				});
				
				// 模拟提交延迟
				setTimeout(() => {
					uni.hideLoading();
					uni.showModal({
						title: '提示',
						content: '问卷提交成功！正在为您分析体质类型...',
						showCancel: false,
						success: () => {
							// 跳转到结果页面
							uni.navigateTo({
								url: '/pages/healthresult/healthresult?answers=' + JSON.stringify(answers)
							});
						}
					});
				}, 1500);
				
				// 真实API调用（注释状态）
				// let that = this;
				// util.request(api.SubmitHealthQuestionnaire, { answers: answers }).then(function(res) {
				// 	if (res.errno === 0) {
				// 		uni.navigateTo({
				// 			url: '/pages/healthresult/healthresult?resultId=' + res.data.resultId
				// 		});
				// 	}
				// });
			}
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.getQuestionnaireData();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		
		onLoad: function() {
			this.getQuestionnaireData();
		}
	}
</script>

<style scoped>
	.container {
		width: 100%;
		min-height: 100vh;
		background-color: #f5f1e8;
		position: relative;
		overflow: hidden;
		background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4"><circle cx="1" cy="1" r="0.5" fill="%23e0e0e0"/><circle cx="3" cy="3" r="0.5" fill="%23e0e0e0"/></svg>');
		background-size: 4px 4px;
	}

	/* 顶部说明 */
	.instruction-header {
		position: absolute;
		top: 60px;
		left: 20px;
		width: 335px;
		z-index: 10;
	}

	.instruction-text {
		font-family: serif;
		font-size: 16px;
		line-height: 24px;
		color: #666666;
		text-align: left;
	}

	/* 问题容器 */
	.question-container {
		position: absolute;
		top: 140px;
		width: 100%;
		height: 580px;
		padding: 0 20px;
		box-sizing: border-box;
	}

	/* 问题卡片 */
	.question-card {
		background-color: #ffffff;
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 16px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	}

	.question-card.transparent {
		background-color: transparent;
		box-shadow: none;
		padding: 20px 0px;
	}

	.question-text {
		font-size: 18px;
		font-weight: bold;
		margin-bottom: 15px;
		color: #333;
		display: block;
	}

	/* 选项按钮组 */
	.pill-button-group {
		display: flex;
		flex-direction: row;
		gap: 20px;
		flex-wrap: wrap;
	}

	.pill-button {
		padding: 10px 20px;
		border-radius: 25px;
		border: 1px solid #cccccc;
		background-color: #ffffff;
		transition: all 0.2s ease;
	}

	.pill-button.selected {
		background-color: #d4a574;
		border-color: #d4a574;
	}

	.pill-text {
		color: #666666;
		font-size: 16px;
		display: block;
	}

	.pill-button.selected .pill-text {
		color: #8b4513;
	}

	/* 提交按钮 */
	.submit-button {
		position: absolute;
		bottom: 20px;
		left: 50%;
		transform: translateX(-50%);
		width: 335px;
		height: 50px;
		background-color: #d4a574;
		border-radius: 25px;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 2px 8px rgba(212,165,116,0.3);
		transition: all 0.3s ease;
	}

	.submit-button:active {
		background-color: #c09060;
		transform: translateX(-50%) scale(0.98);
	}

	.submit-text {
		color: #8b4513;
		font-size: 18px;
		font-weight: 500;
		display: block;
	}
</style>
