<template>
	<view class="container">
		<view class="decoration"></view>
		
		<!-- 顶部标题 -->
		<view class="header">
			<view class="back-button" @tap="goBack">←</view>
			<text class="header-title">体质评估结果</text>
		</view>

		<view class="main-content">
			<!-- 体质结果展示 -->
			<view class="results-header">
				<text class="results-title">您的体质是：</text>
			</view>
			
			<view class="constitution-display">
				<view class="body-illustration" role="img" aria-label="中医经络人体图"></view>
				<view class="constitution-label">
					<text>{{ constitutionResult.type || '平和体质' }}</text>
				</view>
			</view>

			<!-- 体质特征 -->
			<view class="characteristics-section">
				<view class="characteristic-block" v-for="(characteristic, index) in characteristics" :key="index">
					<view class="category-label">
						<text>{{ characteristic.category }}</text>
					</view>
					<text class="description-text">{{ characteristic.description }}</text>
				</view>
			</view>

			<!-- 指导意见 -->
			<view class="guidance-section">
				<text class="section-header">指导意见</text>
				
				<view 
					v-for="(guidance, index) in guidanceList" 
					:key="index"
					:class="['expandable-card', { 'expanded': guidance.isExpanded }]"
					@tap="toggleCard(index)"
				>
					<view class="card-header">
						<view class="card-title-container">
							<view class="card-icon">
								<text>{{ guidance.icon }}</text>
							</view>
							<text class="card-title">{{ guidance.title }}</text>
						</view>
						<text :class="['chevron', { 'expanded': guidance.isExpanded }]">▶</text>
					</view>
					<view :class="['card-content', { 'expanded': guidance.isExpanded }]">
						<text class="main-text">{{ guidance.content }}</text>
						
						<!-- 四时饮食调养（仅饮食调养卡片显示） -->
						<view v-if="guidance.seasonal && guidance.isExpanded" class="seasonal-recommendations">
							<text class="seasonal-title">四时饮食调养：</text>
							
							<view 
								v-for="(season, seasonIndex) in guidance.seasonal" 
								:key="seasonIndex"
								class="season-item"
							>
								<text class="season-number">{{ seasonIndex + 1 }}</text>
								<text class="season-text">
									<text class="season-name">{{ season.season }}</text>{{ season.description }}
								</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const util = require("@/utils/util.js");
	const api = require('@/utils/api.js');
	
	export default {
		data() {
			return {
				constitutionResult: {},
				characteristics: [],
				guidanceList: []
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 切换卡片展开状态
			toggleCard(index) {
				this.guidanceList[index].isExpanded = !this.guidanceList[index].isExpanded;
				this.$forceUpdate();
			},
			
			// 获取体质评估结果
			getAssessmentResult() {
				let that = this;
				
				// 显示加载提示
				uni.showLoading({
					title: '加载中...'
				});
				
				// 这里使用模拟数据，实际项目中应该调用真实API
				// util.request(api.AssessmentResult, {}).then(function(res) {
				// 	if (res.errno === 0) {
				// 		that.constitutionResult = res.data.constitutionResult;
				// 		that.characteristics = res.data.characteristics;
				// 		that.guidanceList = res.data.guidanceList;
				// 	}
				// 	uni.hideLoading();
				// });
				
				// 模拟数据
				setTimeout(() => {
					that.constitutionResult = that.getMockConstitutionResult();
					that.characteristics = that.getMockCharacteristics();
					that.guidanceList = that.getMockGuidanceList();
					uni.hideLoading();
				}, 1000);
			},
			
			// 获取模拟体质结果
			getMockConstitutionResult() {
				return {
					type: '平和体质'
				};
			},
			
			// 获取模拟体质特征
			getMockCharacteristics() {
				return [
					{
						category: '形体特征',
						description: '体型匀称、面色红润'
					},
					{
						category: '心理特征',
						description: '心态平和、精力充沛'
					},
					{
						category: '体质特征',
						description: '胃口良好、睡眠安稳、脉和有神、两便正常、目光有神、舌苔颜色正常。'
					},
					{
						category: '日常调理',
						description: '心态平和，继续保持，适量运动'
					}
				];
			},
			
			// 获取模拟指导意见
			getMockGuidanceList() {
				return [
					{
						icon: '♥',
						title: '情志调摄',
						content: '保持心态平和，避免过度情绪波动，适当进行冥想或太极等调节身心的活动。',
						isExpanded: false
					},
					{
						icon: '🍽',
						title: '饮食调养',
						content: '饮食宜粗细粮食合理搭配，多吃五谷杂粮、蔬菜瓜果，少食过于油腻及辛辣食品；不要过饥过饱，也不要进食过冷过烫或不干净食物；注意戒烟限酒。',
						isExpanded: true,
						seasonal: [
							{
								season: '春',
								description: '宜多食蔬菜，如菠菜、芹菜、春笋、苋菜等'
							},
							{
								season: '夏',
								description: '宜多食新鲜水果，如西瓜、番茄、菠萝等，其他清凉生津食品，如金银花、菊花、鲜芦根、绿豆、冬瓜、苦瓜、黄瓜、生菜、豆芽等均可酌情食用，以清热祛暑'
							},
							{
								season: '长夏',
								description: '宜选用茯苓、霍香、山药、莲子、薏仁、扁豆、丝瓜等利湿健脾之品。不宜进食滋腻碍胃的食物'
							},
							{
								season: '秋',
								description: '宜选用寒温偏性不明显的平性药食。同时，宜食用滋润滋阴之品以保护阴津，如沙参、麦冬、阿胶、甘草等'
							},
							{
								season: '冬',
								description: '宜选用温补之品，如生姜、肉桂、羊肉等温补之品'
							}
						]
					}
				];
			}
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.getAssessmentResult();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		
		onLoad: function(options) {
			this.getAssessmentResult();
		}
	}
</script>

<style lang="scss">
	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	.container {
		max-width: 750rpx;
		margin: 0 auto;
		background: #f5f1e8;
		background-image:
			radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px),
			radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 1px, transparent 1px);
		background-size: 40rpx 40rpx;
		min-height: 100vh;
		color: #333333;
		line-height: 1.6;
		position: relative;
	}

	.decoration {
		position: absolute;
		width: 100%;
		height: 100%;
		pointer-events: none;
		opacity: 0.05;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/><path d="M25,25 L75,25 L75,75 L25,75 Z" fill="%238B4513"/><path d="M37.5,37.5 L62.5,37.5 L62.5,62.5 L37.5,62.5 Z" fill="%238B4513"/></svg>');
		background-size: 100rpx 100rpx;
	}

	.header {
		background: linear-gradient(90deg, #f9f6f2 80%, #f3e9d7 100%);
		color: #8B4513;
		padding: 40rpx 30rpx;
		text-align: center;
		position: relative;
		border-radius: 0 0 36rpx 36rpx;
		margin: 0 20rpx 20rpx 20rpx;
		box-shadow: 0 4rpx 16rpx rgba(139,69,19,0.04);
	}

	.header-title {
		font-size: 48rpx;
		letter-spacing: 8rpx;
		font-weight: bold;
		color: #8B4513;
	}

	.back-button {
		position: absolute;
		left: 30rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #8B4513;
		font-size: 36rpx;
		font-weight: bold;
	}

	.main-content {
		padding: 40rpx;
		margin-bottom: 120rpx;
	}

	.results-header {
		margin: 40rpx 0 20rpx 0;
	}

	.results-title {
		font-size: 40rpx;
		font-weight: 500;
		color: #333333;
	}

	.constitution-display {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
		padding: 20rpx 0;
	}

	.body-illustration {
		width: 160rpx;
		height: 160rpx;
		background: url('/static/images/doctor/body-illustration.png') no-repeat center;
		background-size: contain;
		margin-right: 40rpx;
		border: 4rpx solid #d4a574;
		border-radius: 16rpx;
		padding: 20rpx;
	}

	.constitution-label {
		background-color: #d4a574;
		color: #ffffff;
		padding: 20rpx 40rpx;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: 500;
		text-align: center;
	}

	.characteristics-section {
		margin-bottom: 40rpx;
	}

	.characteristic-block {
		margin-bottom: 32rpx;
		line-height: 1.8;
		display: flex;
		align-items: flex-start;
		flex-wrap: wrap;
	}

	.category-label {
		background-color: #d4a574;
		color: #ffffff;
		padding: 8rpx 24rpx;
		border-radius: 24rpx;
		font-size: 28rpx;
		font-weight: 500;
		margin-right: 16rpx;
		margin-bottom: 8rpx;
	}

	.description-text {
		color: #666666;
		font-size: 32rpx;
		flex: 1;
		min-width: 200rpx;
	}

	.guidance-section {
		margin-top: 40rpx;
	}

	.section-header {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 40rpx;
	}

	.expandable-card {
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 40rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
		transition: all 0.3s ease;
	}

	.expandable-card:active {
		box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
	}

	.card-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.card-title-container {
		display: flex;
		align-items: center;
	}

	.card-icon {
		width: 48rpx;
		height: 48rpx;
		background-color: #d4a574;
		border-radius: 50%;
		margin-right: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: white;
		font-size: 24rpx;
	}

	.card-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333333;
	}

	.chevron {
		width: 40rpx;
		height: 40rpx;
		color: #999999;
		transition: transform 0.3s ease;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.chevron.expanded {
		transform: rotate(90deg);
	}

	.card-content {
		margin-top: 32rpx;
		display: none;
		animation: slideDown 0.3s ease;
	}

	.card-content.expanded {
		display: block;
	}

	@keyframes slideDown {
		from {
			opacity: 0;
			transform: translateY(-20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.main-text {
		color: #666666;
		font-size: 30rpx;
		line-height: 1.6;
		margin-bottom: 32rpx;
	}

	.seasonal-title {
		font-weight: 600;
		color: #333333;
		margin-bottom: 24rpx;
		font-size: 30rpx;
	}

	.season-item {
		margin-bottom: 24rpx;
		padding-left: 40rpx;
		position: relative;
	}

	.season-number {
		position: absolute;
		left: 0;
		top: 0;
		color: #d4a574;
		font-weight: bold;
		font-size: 28rpx;
	}

	.season-text {
		color: #666666;
		font-size: 28rpx;
		line-height: 1.5;
	}

	.season-name {
		font-weight: bold;
		color: #333333;
	}

	/* 响应式设计 */
	@media (max-width: 375px) {
		.main-content {
			padding: 30rpx;
		}

		.constitution-display {
			flex-direction: column;
			text-align: center;
		}

		.body-illustration {
			margin-right: 0;
			margin-bottom: 30rpx;
		}
	}
</style>
