<template>
	<view class="container">
		<view class="decoration"></view>
		
		<!-- 顶部标题 -->
		<view class="header-title">
			<text class="title-text">您知道自己属于哪种体质吗？</text>
		</view>

		<!-- 体质网格 -->
		<view class="constitution-grid">
			<view 
				v-for="(constitution, index) in constitutions" 
				:key="constitution.id"
				:class="['circular-button', { 'active': selectedConstitution === constitution.id, 'balanced': constitution.id === 'balanced_constitution' }]"
				@tap="selectConstitution(constitution.id)"
			>
				<text class="primary-text">{{ constitution.name }}</text>
				<text class="secondary-text">体质</text>
			</view>
		</view>

		<!-- 描述文字 -->
		<view class="description-text">
			<text class="description">体质是您身体健康特征的描述，了解自己的体质可以更好的了解自己！</text>
		</view>

		<!-- 测试按钮 -->
		<view class="test-button" @tap="startTest">
			<text class="button-text">测试我的体质</text>
		</view>
	</view>
</template>

<script>
	const util = require("@/utils/util.js");
	const api = require('@/utils/api.js');
	
	export default {
		data() {
			return {
				selectedConstitution: 'balanced_constitution',
				constitutions: []
			}
		},
		methods: {
			// 获取体质数据
			getConstitutionData() {
				uni.showLoading({
					title: '加载中...'
				});
				
				// 使用模拟数据，实际项目中可以取消注释下面的API调用
				this.constitutions = this.getMockConstitutions();
				
				setTimeout(() => {
					uni.hideLoading();
				}, 1000);
				
				// 真实API调用（注释状态）
				// let that = this;
				// util.request(api.ConstitutionTypes).then(function(res) {
				// 	if (res.errno === 0) {
				// 		that.constitutions = res.data.data;
				// 	}
				// });
			},
			
			// 模拟体质数据
			getMockConstitutions() {
				return [
					{ id: 'qi_deficiency', name: '气虚', description: '气虚体质特征' },
					{ id: 'phlegm_dampness', name: '痰湿', description: '痰湿体质特征' },
					{ id: 'qi_stagnation', name: '气郁', description: '气郁体质特征' },
					{ id: 'special_constitution', name: '特禀', description: '特禀体质特征' },
					{ id: 'balanced_constitution', name: '平和', description: '平和体质特征' },
					{ id: 'damp_heat', name: '湿热', description: '湿热体质特征' },
					{ id: 'yang_deficiency', name: '阳虚', description: '阳虚体质特征' },
					{ id: 'yin_deficiency', name: '阴虚', description: '阴虚体质特征' },
					{ id: 'blood_stasis', name: '血瘀', description: '血瘀体质特征' }
				];
			},
			
			// 选择体质
			selectConstitution(constitutionId) {
				this.selectedConstitution = constitutionId;
			},
			
			// 开始测试
			startTest() {
				uni.showModal({
					title: '提示',
					content: '即将开始体质测试，请根据实际情况回答问题',
					success: (res) => {
						if (res.confirm) {
							// 跳转到体质问卷页面
							uni.navigateTo({
								url: '/pages/healthassement/healthquest'
							});
						}
					}
				});
			}
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.getConstitutionData();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		
		onLoad: function() {
			this.getConstitutionData();
		}
	}
</script>

<style scoped>
	.container {
		width: 100%;
		min-height: 100vh;
		background-color: #f8f8f8;
		position: relative;
		padding-bottom: 20px;
		background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><circle cx="1" cy="1" r="1" fill="%23e0e0e0"/><circle cx="6" cy="6" r="1" fill="%23e0e0e0"/></svg>');
		background-size: 10px 10px;
	}

	/* 顶部标题 */
	.header-title {
		width: 100%;
		padding-top: 40px;
		text-align: center;
	}

	.title-text {
		font-size: 20px;
		font-weight: bold;
		color: #2c2c2c;
		font-family: serif;
	}

	/* 体质网格 */
	.constitution-grid {
		margin-top: 40px;
		width: 280px;
		height: 280px;
		margin-left: auto;
		margin-right: auto;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-template-rows: repeat(3, 1fr);
		gap: 15px;
		justify-items: center;
		align-items: center;
	}

	/* 圆形按钮 */
	.circular-button {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 70px;
		height: 70px;
		border-radius: 50%;
		background-color: #fff;
		border: 2px solid #d4a574;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		transition: all 0.2s ease-in-out;
	}

	.circular-button.balanced {
		width: 85px;
		height: 85px;
		border: 3px solid #8b4513;
		box-shadow: 0 4px 12px rgba(0,0,0,0.15);
	}

	.circular-button.active {
		border: 3px solid #8b4513;
		box-shadow: 0 4px 12px rgba(0,0,0,0.15);
		transform: scale(1.05);
	}

	.primary-text {
		font-size: 14px;
		font-weight: bold;
		color: #8b4513;
		display: block;
	}

	.balanced .primary-text {
		font-size: 16px;
	}

	.secondary-text {
		font-size: 10px;
		color: #8b4513;
		display: block;
	}

	.balanced .secondary-text {
		font-size: 12px;
	}

	/* 描述文字 */
	.description-text {
		margin-top: 30px;
		width: 300px;
		margin-left: auto;
		margin-right: auto;
		text-align: center;
	}

	.description {
		font-family: serif;
		font-size: 14px;
		line-height: 20px;
		color: #666666;
	}

	/* 测试按钮 */
	.test-button {
		margin-top: 30px;
		margin-bottom: 30px;
		width: 280px;
		height: 45px;
		background-color: #d4a574;
		border-radius: 25px;
		margin-left: auto;
		margin-right: auto;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 2px 8px rgba(212,165,116,0.3);
		transition: all 0.3s ease;
	}

	.test-button:active {
		background-color: #c09060;
		transform: scale(0.98);
	}

	.button-text {
		color: #8b4513;
		font-size: 16px;
		font-weight: 500;
	}

	/* 装饰元素 */
	.decoration {
		position: absolute;
		width: 100%;
		height: 100%;
		pointer-events: none;
		opacity: 0.03;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,10 Q60,30 50,50 Q40,30 50,10 M50,50 Q60,70 50,90 Q40,70 50,50" fill="%238B4513"/><circle cx="20" cy="20" r="3" fill="%23DAA520"/><circle cx="80" cy="80" r="3" fill="%23DAA520"/></svg>');
		background-size: 80px 80px;
	}
</style>
